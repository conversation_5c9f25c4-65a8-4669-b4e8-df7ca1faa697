{"title": "接口配置", "createConfig": "创建配置", "createConfigDesc": "创建接口配置", "editConfig": "编辑配置", "editConfigDesc": "编辑接口配置", "deleteConfig": "删除配置", "configName": "配置名称", "configToken": "配置令牌", "configDescription": "配置描述", "requestMethod": "请求方式", "parsingRules": "解析规则", "basicInfo": "基本信息", "channelSettings": "渠道设置", "configStatus": "配置状态", "enabled": "已启用", "disabled": "已禁用", "enableConfig": "启用配置", "disableConfig": "禁用配置", "searchPlaceholder": "搜索配置名称或描述...", "allStatus": "全部状态", "noConfigData": "暂无配置数据", "required": "必填", "configNamePlaceholder": "请输入配置名称", "configDescPlaceholder": "请输入配置描述", "selectMethodPlaceholder": "请选择请求方式", "selectChannelPlaceholder": "请选择推送渠道", "configNameRequired": "配置名称不能为空", "requestMethodRequired": "请求方式不能为空", "channelRequired": "请至少选择一个推送渠道", "selectMethodFirst": "请先选择请求方式", "selectMethodHint": "需要选择请求方式后才能配置解析规则", "cancel": "取消", "save": "保存", "saving": "保存中...", "newParam": "新参数", "variable": "变量", "messageExtractionRules": "消息提取规则", "contentType": "内容类型", "variableMapping": "变量映射", "addMapping": "添加映射", "noParameterMapping": "暂无参数映射，点击\"添加映射\"开始配置", "jsonPathPlaceholder": "JSON路径 (如: user.name, info.address)", "interfaceParamPlaceholder": "接口参数名", "templateVariablePlaceholder": "模板变量名", "regexRules": "正则表达式规则", "addRule": "添加规则", "noRegexRules": "暂无正则表达式规则，点击\"添加规则\"开始配置", "ruleNumber": "规则 {{number}}", "variableName": "变量名", "regexExpression": "正则表达式", "templateVariableNamePlaceholder": "模板中使用的变量名", "regexPatternPlaceholder": "如: 我叫(.+)", "requestParamPlaceholder": "请求参数", "variableNamePlaceholder": "变量名", "formFieldMapping": "配置表单字段映射，将接口传递的字段名映射为模板中使用的变量名", "jsonFieldMapping": "配置 json 字段路径映射，支持提取多层级的字段", "regexExtraction": "使用正则表达式从文本中提取数据，每个正则表达式提取一个变量"}